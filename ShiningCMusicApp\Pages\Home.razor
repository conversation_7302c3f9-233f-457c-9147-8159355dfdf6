@page "/"
@using ShiningCMusicCommon.Enums
@attribute [Authorize]
@inherits HomeBase

<PageTitle>Shining C Music School</PageTitle>

<div class="container mt-4">
    <div class="row">
        <div class="col-12 text-center">
            <img src="/logo.png" class="logo" />
@*             <h1 class="display-4">🎵 Shining C Music Studio</h1>*@
            <p class="lead">Welcome to our music lesson management system</p>
        </div>
    </div>

    <AuthorizeView Roles="@UserRoleEnum.Administrator.ToString()">
        <div class="row mt-5">
            <div class="col-md-6 offset-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Lesson Time Table</h5>
                        <p class="card-text">View and manage music lesson schedules for tutors and students.</p>
                        <a href="/lessons" class="btn btn-primary btn-lg">
                            <i class="bi bi-calendar3"></i> Open Scheduler
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-md-3 d-flex">
                <div class="card flex-fill">
                    <div class="card-body text-center d-flex flex-column">
                        <h6 class="card-title">📋 Student Timesheets</h6>
                        <p class="card-text flex-grow-1">Track student attendance with digital timesheet records including date, time, and signatures</p>
                        <a href="/timesheets" class="btn btn-outline-primary mt-auto">
                            <i class="bi bi-clipboard-check"></i> Manage Timesheets
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 d-flex">
                <div class="card flex-fill">
                    <div class="card-body text-center d-flex flex-column">
                        <h6 class="card-title">👨‍🏫 Manage Tutors</h6>
                        <p class="card-text flex-grow-1">Add, edit, and manage tutor information</p>
                        <a href="/tutors" class="btn btn-outline-primary mt-auto">
                            <i class="bi bi-person"></i> Manage Tutors
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 d-flex">
                <div class="card flex-fill">
                    <div class="card-body text-center d-flex flex-column">
                        <h6 class="card-title">👨‍🎓 Manage Students</h6>
                        <p class="card-text flex-grow-1">Add, edit, and manage student information</p>
                        <a href="/students" class="btn btn-outline-primary mt-auto">
                            <i class="bi bi-people"></i> Manage Students
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 d-flex">
                <div class="card flex-fill">
                    <div class="card-body text-center d-flex flex-column">
                        <h6 class="card-title">📧 Email Templates</h6>
                        <p class="card-text flex-grow-1">Create and manage professional email templates</p>
                        <a href="/email-templates" class="btn btn-outline-primary mt-auto">
                            <i class="bi bi-envelope"></i> Manage Templates
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-md-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body text-center d-flex flex-column">
                        <h6 class="card-title">⚙️ Maintenance Panel</h6>
                        <p class="card-text flex-grow-1">Maintain system configuration including subjects and locations</p>
                        <a href="/admin" class="btn btn-outline-primary mt-auto">
                            <i class="bi bi-gear"></i> Access Panel
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6 d-flex">
                <div class="card flex-fill">
                    <div class="card-body text-center d-flex flex-column">
                        <h6 class="card-title">🔧 System Settings</h6>
                        <p class="card-text flex-grow-1">Configure application settings and background processors including lesson cleanup, payment reminders, and system preferences</p>
                        <a href="/settings" class="btn btn-outline-primary mt-auto">
                            <i class="bi bi-sliders"></i> Manage Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </AuthorizeView>

    <AuthorizeView Roles="@($"{UserRoleEnum.Tutor},{UserRoleEnum.Student}")">
        <div class="row mt-5">
            <div class="col-md-6 offset-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Lesson Time Table</h5>
                        <p class="card-text">View and export your lessons.</p>
                        <a href="/lessons" class="btn btn-primary btn-lg">
                            <i class="bi bi-calendar3"></i> Open My Lessons
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-4">
            <div class="col-md-6 offset-md-3 d-flex">
                <div class="card flex-fill">
                    <div class="card-body text-center d-flex flex-column">
                        <h6 class="card-title">📋 Student Timesheets</h6>
                        <p class="card-text flex-grow-1">Track your student attendance with digital timesheet records</p>
                        <a href="/timesheets" class="btn btn-outline-primary mt-auto">
                            <i class="bi bi-clipboard-check"></i> My Timesheets
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </AuthorizeView>
</div>
