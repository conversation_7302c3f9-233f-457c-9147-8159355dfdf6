@page "/not-found"
@using ShiningCMusicApp.Services
@inherits NotFoundPageBase

<PageTitle>Page Not Found</PageTitle>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white text-center">
                    <h4 class="mb-0">
                        <i class="bi bi-exclamation-octagon"></i> Page Not Found
                    </h4>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="bi bi-file-earmark-x" style="font-size: 4rem; color: var(--bs-danger);"></i>
                    </div>
                    <h5 class="card-title">404 - The page you're looking for doesn't exist</h5>
                    <p class="card-text text-muted">
                        The page you requested could not be found. It may have been moved, deleted, or you may have entered an incorrect URL.
                    </p>
                    <div class="mt-4">
                        <button class="btn btn-primary me-2" @onclick="GoToHome">
                            <i class="bi bi-house"></i> Go to Home
                        </button>
                        <button class="btn btn-outline-secondary" @onclick="GoBack">
                            <i class="bi bi-arrow-left"></i> Go Back
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
