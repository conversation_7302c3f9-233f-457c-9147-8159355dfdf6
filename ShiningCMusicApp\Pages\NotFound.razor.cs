using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using ShiningCMusicApp.Services;

namespace ShiningCMusicApp.Pages;

public partial class NotFoundPageBase : ComponentBase
{
    [Inject] protected NavigationManager Navigation { get; set; } = default!;
    [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;

    protected void GoToHome()
    {
        Navigation.NavigateTo("/");
    }

    protected async Task GoBack()
    {
        await JSRuntime.InvokeVoidAsync("history.back");
    }
}
